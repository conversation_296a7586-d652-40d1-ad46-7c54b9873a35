import { useMemo } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useNavigate } from '@tanstack/react-router';
import { supabase } from '../lib/supabase';
import { type EnergieausweisData } from '../types/csv';
import { PricingService } from '../services/pricingService';

// Types for section configuration
export interface SectionConfig {
  title: string;
  data: Record<string, any> | null | undefined;
  fields?: string[] | Record<string, string>;
  excludeFields?: string[];
}

export interface SummaryDataReturn {
  // Data
  energieausweisData: EnergieausweisData | null;
  sections: SectionConfig[];
  pricingInfo: any;

  // Status flags
  isPaymentInProgress: boolean;
  isPaymentFailed: boolean;
  isPaid: boolean;
  isPaymentStatus: boolean;

  // Loading states
  isLoading: boolean;
  pricingLoading: boolean;

  // Error states
  isError: boolean;

  // Navigation
  handleBackNavigation: () => void;

  // Certificate type helper
  getCertificateTypeLabel: (type: string | null | undefined) => string;
}

/**
 * Custom hook for managing summary data fetching and processing
 * Handles certificate data, pricing info, and data section configuration
 */
export const useSummaryData = (activeCertificateId: string | null): SummaryDataReturn => {
  const navigate = useNavigate();

  // Fetch all energieausweis data for the current user with optimized caching
  const { data: energieausweisData, isError, isLoading } = useQuery({
    queryKey: ['energieausweise', 'all', activeCertificateId],
    queryFn: async () => {
      console.log('🔍 Fetching energieausweis data for:', activeCertificateId);
      if (!activeCertificateId) throw new Error('Kein aktives Zertifikat ausgewählt.');

      const { data, error } = await supabase
        .from('energieausweise')
        .select('*')
        .eq('id', activeCertificateId)
        .single();

      if (error) {
        throw error;
      }

      return data as EnergieausweisData;
    },
    enabled: !!activeCertificateId,
    retry: false, // Override global default for this critical query
    staleTime: 10 * 60 * 1000, // 10 minutes - longer cache for summary data
    gcTime: 15 * 60 * 1000, // 15 minutes - keep in cache longer
    refetchOnWindowFocus: false, // Prevent refetch on tab switching
    refetchOnMount: false, // Prevent refetch on component remount
  });

  // Fetch pricing information for the current certificate type
  const { data: pricingInfo, isLoading: pricingLoading } = useQuery({
    queryKey: ['pricing', energieausweisData?.certificate_type],
    queryFn: async () => {
      if (!energieausweisData?.certificate_type) return null;
      return await PricingService.getPricingDisplayInfoForType(energieausweisData.certificate_type as any);
    },
    enabled: !!energieausweisData?.certificate_type,
    retry: false,
    staleTime: 15 * 60 * 1000, // 15 minutes - pricing rarely changes
    gcTime: 30 * 60 * 1000, // 30 minutes - keep pricing in cache longer
    refetchOnWindowFocus: false, // Prevent refetch on tab switching
    refetchOnMount: false, // Prevent refetch on component remount
  });

  // Payment status checks
  const isPaymentInProgress = energieausweisData?.status === 'payment_initiated';
  const isPaymentFailed = Boolean(energieausweisData?.status &&
    ['payment_failed', 'payment_disputed', 'payment_expired'].includes(energieausweisData.status));
  const isPaid = energieausweisData?.status === 'payment_complete';

  // Legacy check for backward compatibility
  const isPaymentStatus = Boolean(energieausweisData?.status &&
    ['payment_initiated', 'payment_complete', 'payment_failed', 'payment_disputed', 'payment_expired'].includes(energieausweisData.status));

  // Get certificate type label
  const getCertificateTypeLabel = (type: string | null | undefined) => {
    switch (type) {
      case 'WG/V':
        return 'Wohngebäude-Verbrauchsausweis';
      case 'WG/B':
        return 'Wohngebäude-Bedarfsausweis';
      case 'NWG/V':
        return 'Nicht-Wohngebäude-Verbrauchsausweis';
      default:
        return 'Nicht angegeben';
    }
  };

  // Function to handle back button navigation based on certificate type
  const handleBackNavigation = () => {
    if (!energieausweisData?.certificate_type) {
      // Fallback to objektdaten page if certificate type is not available
      navigate({ to: '/erfassen/objektdaten' });
      return;
    }

    // Navigate based on certificate type following the established routing flow
    if (energieausweisData.certificate_type === 'WG/B') {
      // For WG/B: Navigate back to TwwLueftungPage (since VerbrauchPage is skipped)
      navigate({ to: '/erfassen/tww-lueftung' });
    } else {
      // For WG/V and NWG/V: Navigate back to VerbrauchPage (since TwwLueftungPage is skipped)
      navigate({ to: '/erfassen/verbrauch' });
    }
  };

  // Customer fields to be excluded from Objektdaten
  const kundenFields = [
    'Kunden_Anrede', 'Kunden_Vorname', 'Kunden_Nachname', 'Kunden_Strasse',
    'Kunden_Hausnr', 'Kunden_PLZ', 'Kunden_Ort', 'Kunden_email', 'Kunden_telefon'
  ];

  // Memoize individual data sections to prevent unnecessary recalculations
  const orderInfo = useMemo(() => ({
    order_number: energieausweisData?.order_number || `EA-${energieausweisData?.id?.slice(-8).toUpperCase()}`
  }), [energieausweisData?.order_number, energieausweisData?.id]);

  const certificateTypeInfo = useMemo(() => ({
    certificate_type: getCertificateTypeLabel(energieausweisData?.certificate_type)
  }), [energieausweisData?.certificate_type]);

  // Memoize processed building details to avoid expensive array operations on every render
  const processedGebaeudedetails2 = useMemo(() => {
    if (!energieausweisData?.gebaeudedetails2) return null;

    const details = { ...energieausweisData.gebaeudedetails2 };

    // Process boeden array if it exists
    if (details.boeden && Array.isArray(details.boeden)) {
      details.boeden.forEach((boden: any, index: number) => {
        const prefix = `Boden${index + 1}`;
        details[prefix] = boden.bezeichnung;
        details[`${prefix}_massiv`] = boden.massiv;
        details[`${prefix}_Kellerdecke`] = boden.uebergang;
        details[`${prefix}_Fläche`] = boden.flaeche;
        details[`${prefix}_Dämmung`] = boden.daemmung;
      });
    }

    // Process daecher array if it exists
    if (details.daecher && Array.isArray(details.daecher)) {
      details.daecher.forEach((dach: any, index: number) => {
        const prefix = `Dach${index + 1}`;
        details[prefix] = dach.bezeichnung;
        details[`${prefix}_massiv`] = dach.massiv;
        details[`${prefix}_Geschossdecke`] = dach.uebergang;
        details[`${prefix}_Fläche`] = dach.flaeche;
        details[`${prefix}_Dämmung`] = dach.daemmung;
      });
    }

    // Process waende array if it exists
    if (details.waende && Array.isArray(details.waende)) {
      details.waende.forEach((wand: any, index: number) => {
        const prefix = `Wand${index + 1}`;
        details[prefix] = wand.bezeichnung;
        details[`${prefix}_massiv`] = wand.massiv;
        details[`${prefix}_Fläche`] = wand.flaeche;
        details[`${prefix}_Dämmung`] = wand.daemmung;
      });
    }

    return details;
  }, [energieausweisData?.gebaeudedetails2]);

  // Memoize processed fenster data
  const processedFensterData = useMemo(() => {
    if (!energieausweisData?.fenster?.fenster) return null;

    const result: Record<string, any> = {};
    energieausweisData.fenster.fenster.forEach((fenster: any, index: number) => {
      const prefix = `Fenster${index + 1}_`;
      result[`${prefix}bezeichnung`] = fenster.bezeichnung;
      result[`${prefix}art`] = fenster.art;
      result[`${prefix}flaeche`] = fenster.flaeche;
      result[`${prefix}ausrichtung`] = fenster.ausrichtung;
      result[`${prefix}baujahr`] = fenster.baujahr;
    });
    return result;
  }, [energieausweisData?.fenster?.fenster]);

  // Memoize combined TWW & Lüftung data
  const combinedTwwLueftungData = useMemo(() => ({
    ...(energieausweisData?.trinkwarmwasser || {}),
    ...(energieausweisData?.lueftung || {})
  }), [energieausweisData?.trinkwarmwasser, energieausweisData?.lueftung]);

  // Organize data into sections with optimized dependencies
  const sections: SectionConfig[] = useMemo(() => {
    if (!energieausweisData) return [];

    const baseSections = [
      {
        title: 'Bestellinformationen',
        data: orderInfo,
        fields: { order_number: 'Bestellnummer' } as Record<string, string>,
        excludeFields: []
      },
      {
        title: 'Ausweistyp',
        data: certificateTypeInfo,
        fields: { certificate_type: 'Energieausweistyp' } as Record<string, string>,
        excludeFields: []
      },
      {
        title: 'Objektdaten',
        data: energieausweisData.objektdaten,
        excludeFields: kundenFields
      },
      {
        title: 'Kundendaten',
        data: energieausweisData.objektdaten,
        fields: kundenFields
      },
      {
        title: 'Gebäudedetails (Teil 1)',
        data: energieausweisData.gebaeudedetails1
      },
      {
        title: 'Gebäudedetails (Teil 2)',
        data: processedGebaeudedetails2,
        excludeFields: ['boeden', 'daecher', 'waende']
      }
    ];

    // Add conditional sections based on certificate type
    if (energieausweisData.certificate_type === 'WG/B') {
      baseSections.push(
        {
          title: 'Fenster',
          data: processedFensterData
        },
        {
          title: 'Heizung',
          data: energieausweisData.heizung
        },
        {
          title: 'Trinkwarmwasser & Lüftung',
          data: combinedTwwLueftungData
        }
      );
    }

    if (energieausweisData.certificate_type === 'WG/V' || energieausweisData.certificate_type === 'NWG/V') {
      baseSections.push({
        title: 'Verbrauchsdaten',
        data: energieausweisData.verbrauchsdaten
      });
    }

    return baseSections;
  }, [
    energieausweisData?.certificate_type,
    orderInfo,
    certificateTypeInfo,
    energieausweisData?.objektdaten,
    energieausweisData?.gebaeudedetails1,
    processedGebaeudedetails2,
    processedFensterData,
    energieausweisData?.heizung,
    combinedTwwLueftungData,
    energieausweisData?.verbrauchsdaten
  ]);

  return {
    // Data
    energieausweisData: energieausweisData || null,
    sections,
    pricingInfo,

    // Status flags
    isPaymentInProgress,
    isPaymentFailed,
    isPaid,
    isPaymentStatus,

    // Loading states
    isLoading,
    pricingLoading,

    // Error states
    isError,

    // Navigation
    handleBackNavigation,

    // Certificate type helper
    getCertificateTypeLabel,
  };
};