import React from 'react';
import { useCertificate } from '../../contexts/CertificateContext';
import { useAuth } from '../../contexts/AuthContext';
import { useCertificateFiles } from '../../hooks/useCertificateFiles';
import { usePageVisit } from '../../hooks/usePageVisit';
import { AccountConversionModal } from '../../components/auth/AccountConversionModal';

// Import custom hooks
import { useSummaryData } from '../../hooks/useSummaryData';
import { usePaymentFlow } from '../../hooks/usePaymentFlow';
import { useLegalConsent } from '../../hooks/useLegalConsent';
import { useAccountConversion } from '../../hooks/useAccountConversion';

// Import presentational components
import {
  SummaryHeader,
  PaymentStatusBanner,
  DataSummaryDisplay,
  PricingOverview,
  LegalConsentSection,
  SummaryActions
} from '../../components/summary';
import CertificateOrderingInfo from '../../components/CertificateOrderingInfo';

console.log('🚀 ZusammenfassungPage loaded') ;
/**
 * Refactored ZusammenfassungPage component using the new architecture
 * This component now orchestrates custom hooks and presentational components
 * instead of handling all business logic internally
 */
export const ZusammenfassungPage: React.FC = () => {
  // Context hooks
  const { activeCertificateId } = useCertificate();
  const { isAnonymous } = useAuth();

  console.log('🏠 ZusammenfassungPage render:', activeCertificateId);
  
  // Page visit tracking
  usePageVisit('zusammenfassung');

  // Custom hooks for business logic
  const {
    energieausweisData,
    sections,
    pricingInfo,
    isPaymentFailed,
    isPaid,
    isLoading,
    pricingLoading,
    isError,
    handleBackNavigation
  } = useSummaryData(activeCertificateId);

  const {
    isProcessingPayment,
    error: paymentError,
    handleCheckout,
    setError: setPaymentError,
    setIsProcessingPayment
  } = usePaymentFlow(activeCertificateId, energieausweisData);

  const {
    legalConsent,
    isValid: isLegalConsentValid,
    updateConsent,
    getValidationMessage
  } = useLegalConsent();

  const {
    showAccountConversion,
    userEmail,
    setShowAccountConversion,
    handleAccountConversionSuccess,
    handleAccountConversionClose
  } = useAccountConversion(activeCertificateId);

  // File management
  const { filesByField } = useCertificateFiles(activeCertificateId);

  // Handle account conversion success
  const onAccountConversionSuccess = () => {
    handleAccountConversionSuccess(() => {
      // Retry checkout after successful account conversion
      handleCheckout(legalConsent, false, () => setShowAccountConversion(true));
    });
  };

  // Handle account conversion close
  const onAccountConversionClose = () => {
    handleAccountConversionClose(() => {
      setIsProcessingPayment(false);
      setPaymentError(null);
    });
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
            <div className="h-6 bg-gray-200 rounded w-1/2 mb-8"></div>
            <div className="space-y-6">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="bg-white shadow rounded-lg p-6">
                  <div className="h-4 bg-gray-200 rounded w-1/3 mb-4"></div>
                  <div className="space-y-2">
                    <div className="h-3 bg-gray-200 rounded w-full"></div>
                    <div className="h-3 bg-gray-200 rounded w-3/4"></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (isError || !energieausweisData) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-red-50 border border-red-200 rounded-lg p-6">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">
                  Fehler beim Laden der Daten
                </h3>
                <div className="mt-2 text-sm text-red-700">
                  <p>
                    Die Energieausweis-Daten konnten nicht geladen werden. 
                    Bitte versuchen Sie es erneut oder wenden Sie sich an den Support.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Main render
  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header Section */}
        <SummaryHeader />

        {/* Payment Status Banner */}
        <PaymentStatusBanner 
          status={energieausweisData.status}
          certificateData={energieausweisData}
          orderNumber={energieausweisData.order_number || `EA-${energieausweisData.id?.slice(-8).toUpperCase()}`}
        />

        {/* Data Summary Display */}
        <DataSummaryDisplay 
          sections={sections}
          filesByField={filesByField}
          activeCertificateId={activeCertificateId}
        />

        {/* Pricing Overview */}
        <PricingOverview 
          pricingInfo={pricingInfo}
          certificateType={energieausweisData.certificate_type}
          loading={pricingLoading}
        />

        {/* Certificate Ordering Information */}
        <CertificateOrderingInfo />

        {/* Legal Consent Section - Only show if not paid */}
        <LegalConsentSection 
          legalConsent={legalConsent}
          updateConsent={updateConsent}
          isValid={isLegalConsentValid}
          showSection={!isPaid}
          getValidationMessage={getValidationMessage}
        />

        {/* Action Buttons */}
        <SummaryActions 
          onCheckout={handleCheckout}
          onBack={handleBackNavigation}
          isProcessingPayment={isProcessingPayment}
          error={paymentError}
          legalConsent={legalConsent}
          isValid={isLegalConsentValid}
          paymentStatus={energieausweisData.status}
          isPaid={isPaid}
          isPaymentFailed={isPaymentFailed}
          isAnonymous={isAnonymous}
          onShowAccountConversion={() => setShowAccountConversion(true)}
        />

        {/* Account Conversion Modal for Anonymous Users */}
        {isAnonymous && userEmail && showAccountConversion && (
          <AccountConversionModal
            isOpen={showAccountConversion}
            onClose={onAccountConversionClose}
            onSuccess={onAccountConversionSuccess}
            userEmail={userEmail}
          />
        )}
      </div>
    </div>
  );
};
